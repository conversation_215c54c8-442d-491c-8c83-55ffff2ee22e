{"name": "mcq-quiz-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for MCQ Quiz System", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^11.11.1", "firebase-functions": "^4.5.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8", "joi": "^17.11.0", "lodash": "^4.17.21", "moment": "^2.29.4", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "twilio": "^4.19.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/lodash": "^4.14.202", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.55.0", "eslint-plugin-import": "^2.29.0", "firebase-functions-test": "^3.1.1", "typescript": "^5.3.2"}, "private": true}