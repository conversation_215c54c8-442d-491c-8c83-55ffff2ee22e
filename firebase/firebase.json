{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"source": "functions", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, "hosting": [{"target": "web-admin", "public": "../web_admin/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}], "storage": {"rules": "storage.rules"}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true}}}