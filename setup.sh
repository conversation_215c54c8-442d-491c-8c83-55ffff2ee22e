#!/bin/bash

# MCQ Quiz System - Master Setup Script
# This script orchestrates the complete setup process

set -e

echo "🎯 MCQ Quiz System - Complete Setup"
echo "==================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

print_step() {
    echo -e "${MAGENTA}$1${NC}"
}

# Make all scripts executable
make_scripts_executable() {
    print_info "Making setup scripts executable..."
    chmod +x scripts/*.sh
    chmod +x scripts/*.js 2>/dev/null || true
    print_status "Scripts are now executable"
}

# Display welcome message
show_welcome() {
    clear
    echo ""
    print_header "╔══════════════════════════════════════════════════════════════╗"
    print_header "║                    MCQ Quiz System Setup                    ║"
    print_header "║              Post Office Departmental Exam Prep             ║"
    print_header "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    print_info "This setup wizard will guide you through the complete installation"
    print_info "and configuration of the MCQ Quiz System."
    echo ""
    print_warning "Prerequisites:"
    echo "• Firebase account and project"
    echo "• Flutter SDK (for mobile app)"
    echo "• Node.js 16+ (for web admin)"
    echo "• Git (for version control)"
    echo ""
    read -p "Press Enter to continue..."
}

# Show setup menu
show_menu() {
    clear
    print_header "🚀 MCQ Quiz System Setup Menu"
    print_header "=============================="
    echo ""
    echo "1. 🔥 Firebase Project Setup"
    echo "2. 📱 Mobile App Configuration"
    echo "3. 🌐 Web Admin Panel Setup"
    echo "4. 🗄️  Database Initialization"
    echo "5. 👤 Admin User Setup"
    echo "6. 🧪 System Testing"
    echo "7. 🚀 Production Deployment"
    echo "8. 📚 View Documentation"
    echo "9. ❓ Help & Troubleshooting"
    echo "0. 🚪 Exit"
    echo ""
    print_info "Choose an option (0-9):"
}

# Firebase setup
setup_firebase() {
    print_step "🔥 Starting Firebase Setup..."
    echo ""
    
    if [ -f "scripts/setup-firebase.sh" ]; then
        bash scripts/setup-firebase.sh
    else
        print_error "Firebase setup script not found"
        return 1
    fi
    
    print_status "Firebase setup completed"
    read -p "Press Enter to continue..."
}

# Mobile app setup
setup_mobile() {
    print_step "📱 Starting Mobile App Setup..."
    echo ""
    
    if [ -f "scripts/setup-mobile.sh" ]; then
        bash scripts/setup-mobile.sh
    else
        print_error "Mobile setup script not found"
        return 1
    fi
    
    print_status "Mobile app setup completed"
    read -p "Press Enter to continue..."
}

# Web admin setup
setup_web() {
    print_step "🌐 Starting Web Admin Setup..."
    echo ""
    
    if [ -f "scripts/setup-web.sh" ]; then
        bash scripts/setup-web.sh
    else
        print_error "Web setup script not found"
        return 1
    fi
    
    print_status "Web admin setup completed"
    read -p "Press Enter to continue..."
}

# Database initialization
init_database() {
    print_step "🗄️ Starting Database Initialization..."
    echo ""
    
    if [ -f "scripts/init-database.js" ]; then
        node scripts/init-database.js
    else
        print_error "Database initialization script not found"
        return 1
    fi
    
    print_status "Database initialization completed"
    read -p "Press Enter to continue..."
}

# Admin user setup
setup_admin() {
    print_step "👤 Starting Admin User Setup..."
    echo ""
    
    if [ -f "scripts/setup-admin-user.sh" ]; then
        bash scripts/setup-admin-user.sh
    else
        print_error "Admin user setup script not found"
        return 1
    fi
    
    print_status "Admin user setup completed"
    read -p "Press Enter to continue..."
}

# System testing
test_system() {
    print_step "🧪 Starting System Testing..."
    echo ""
    
    if [ -f "scripts/test-system.sh" ]; then
        bash scripts/test-system.sh
    else
        print_error "System testing script not found"
        return 1
    fi
    
    print_status "System testing completed"
    read -p "Press Enter to continue..."
}

# Production deployment
deploy_production() {
    print_step "🚀 Starting Production Deployment..."
    echo ""
    
    print_warning "⚠️  PRODUCTION DEPLOYMENT ⚠️"
    echo "This will deploy your system to production."
    echo ""
    read -p "Are you sure you want to continue? (y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -f "scripts/deploy-production.sh" ]; then
            bash scripts/deploy-production.sh
        else
            print_error "Production deployment script not found"
            return 1
        fi
        
        print_status "Production deployment completed"
    else
        print_info "Production deployment cancelled"
    fi
    
    read -p "Press Enter to continue..."
}

# View documentation
view_docs() {
    clear
    print_header "📚 Documentation"
    print_header "==============="
    echo ""
    
    print_info "Available documentation:"
    echo ""
    echo "1. 📖 Setup Guide (docs/setup-guide.md)"
    echo "2. 🔧 API Documentation (docs/api-documentation.md)"
    echo "3. 🗄️  Firebase Data Structure (docs/firebase-data-structure.md)"
    echo "4. 🎨 UI Design Guide (docs/ui-design-guide.md)"
    echo "5. 🚀 Deployment Guide (docs/deployment-guide.md)"
    echo ""
    
    read -p "Enter the number to view a document (1-5) or press Enter to go back: " choice
    
    case $choice in
        1) less docs/setup-guide.md 2>/dev/null || cat docs/setup-guide.md ;;
        2) less docs/api-documentation.md 2>/dev/null || cat docs/api-documentation.md ;;
        3) less docs/firebase-data-structure.md 2>/dev/null || cat docs/firebase-data-structure.md ;;
        4) less docs/ui-design-guide.md 2>/dev/null || cat docs/ui-design-guide.md ;;
        5) less docs/deployment-guide.md 2>/dev/null || cat docs/deployment-guide.md ;;
        *) return ;;
    esac
    
    read -p "Press Enter to continue..."
}

# Help and troubleshooting
show_help() {
    clear
    print_header "❓ Help & Troubleshooting"
    print_header "========================"
    echo ""
    
    print_info "Common Issues and Solutions:"
    echo ""
    echo "🔥 Firebase Issues:"
    echo "   • Not logged in: Run 'firebase login'"
    echo "   • Wrong project: Run 'firebase use <project-id>'"
    echo "   • Permission denied: Check Firebase project permissions"
    echo ""
    echo "📱 Flutter Issues:"
    echo "   • Flutter not found: Install Flutter SDK"
    echo "   • Doctor issues: Run 'flutter doctor' and fix issues"
    echo "   • Build errors: Run 'flutter clean && flutter pub get'"
    echo ""
    echo "🌐 Node.js Issues:"
    echo "   • Node not found: Install Node.js 16+"
    echo "   • npm errors: Delete node_modules and run 'npm install'"
    echo "   • Build failures: Check environment variables"
    echo ""
    echo "🗄️  Database Issues:"
    echo "   • Connection failed: Check Firebase configuration"
    echo "   • Permission denied: Verify Firestore rules"
    echo "   • Data not found: Run database initialization"
    echo ""
    
    print_info "Getting More Help:"
    echo "• Check the documentation in docs/ directory"
    echo "• Review error messages carefully"
    echo "• Check Firebase Console for issues"
    echo "• Verify all prerequisites are installed"
    echo ""
    
    read -p "Press Enter to continue..."
}

# Quick setup (all steps)
quick_setup() {
    print_step "⚡ Quick Setup - Running All Steps"
    print_step "=================================="
    echo ""
    
    print_warning "This will run all setup steps automatically."
    print_warning "Make sure you have all prerequisites installed."
    echo ""
    
    read -p "Continue with quick setup? (y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        return
    fi
    
    echo ""
    print_info "Starting quick setup..."
    
    # Run all setup steps
    setup_firebase || { print_error "Firebase setup failed"; return 1; }
    setup_mobile || { print_error "Mobile setup failed"; return 1; }
    setup_web || { print_error "Web setup failed"; return 1; }
    init_database || { print_error "Database initialization failed"; return 1; }
    
    print_status "Quick setup completed successfully!"
    echo ""
    print_info "Next steps:"
    echo "1. Create admin user using option 5"
    echo "2. Test the system using option 6"
    echo "3. Deploy to production using option 7"
    echo ""
    
    read -p "Press Enter to continue..."
}

# Main menu loop
main_menu() {
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1) setup_firebase ;;
            2) setup_mobile ;;
            3) setup_web ;;
            4) init_database ;;
            5) setup_admin ;;
            6) test_system ;;
            7) deploy_production ;;
            8) view_docs ;;
            9) show_help ;;
            q|Q) quick_setup ;;
            0) 
                print_info "Thank you for using MCQ Quiz System Setup!"
                print_info "For support, check the documentation or create an issue."
                exit 0
                ;;
            *)
                print_error "Invalid option. Please choose 0-9."
                sleep 2
                ;;
        esac
    done
}

# Main function
main() {
    # Check if we're in the right directory
    if [ ! -f "README.md" ] || [ ! -d "scripts" ]; then
        print_error "Please run this script from the MCQ project root directory"
        exit 1
    fi
    
    # Make scripts executable
    make_scripts_executable
    
    # Show welcome message
    show_welcome
    
    # Start main menu
    main_menu
}

# Handle script interruption
trap 'echo ""; print_warning "Setup interrupted. You can resume anytime by running ./setup.sh"; exit 1' INT

# Run main function
main
