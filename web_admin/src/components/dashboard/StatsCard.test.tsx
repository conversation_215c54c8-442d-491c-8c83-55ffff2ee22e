import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { People } from '@mui/icons-material';
import { StatsCard } from './StatsCard';

const theme = createTheme();

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('StatsCard', () => {
  const defaultProps = {
    title: 'Total Users',
    value: '1,234',
    change: '+12%',
    changeType: 'positive' as const,
    icon: People,
    color: '#6366F1',
  };

  it('renders correctly with all props', () => {
    renderWithTheme(<StatsCard {...defaultProps} />);
    
    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('1,234')).toBeInTheDocument();
    expect(screen.getByText('+12%')).toBeInTheDocument();
  });

  it('displays positive change correctly', () => {
    renderWithTheme(<StatsCard {...defaultProps} />);
    
    const changeElement = screen.getByText('+12%');
    expect(changeElement).toHaveStyle({ color: '#10B981' }); // Success color
  });

  it('displays negative change correctly', () => {
    const negativeProps = {
      ...defaultProps,
      change: '-5%',
      changeType: 'negative' as const,
    };
    
    renderWithTheme(<StatsCard {...negativeProps} />);
    
    const changeElement = screen.getByText('-5%');
    expect(changeElement).toHaveStyle({ color: '#EF4444' }); // Error color
  });

  it('renders icon with correct color', () => {
    renderWithTheme(<StatsCard {...defaultProps} />);
    
    const iconContainer = screen.getByTestId('stats-card-icon');
    expect(iconContainer).toHaveStyle({ backgroundColor: '#6366F1' });
  });

  it('handles missing change prop', () => {
    const propsWithoutChange = {
      ...defaultProps,
      change: undefined,
      changeType: undefined,
    };
    
    renderWithTheme(<StatsCard {...propsWithoutChange} />);
    
    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('1,234')).toBeInTheDocument();
    expect(screen.queryByText('+12%')).not.toBeInTheDocument();
  });

  it('is accessible', () => {
    renderWithTheme(<StatsCard {...defaultProps} />);
    
    const card = screen.getByRole('article');
    expect(card).toBeInTheDocument();
    
    const title = screen.getByText('Total Users');
    expect(title).toHaveAttribute('aria-level', '3');
  });

  it('has correct ARIA labels', () => {
    renderWithTheme(<StatsCard {...defaultProps} />);
    
    const card = screen.getByLabelText('Total Users statistics');
    expect(card).toBeInTheDocument();
  });
});
