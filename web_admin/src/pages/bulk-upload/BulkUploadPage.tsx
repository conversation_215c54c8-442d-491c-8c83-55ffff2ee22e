import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

const BulkUploadPage: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Bulk Upload
      </Typography>
      
      <Paper sx={{ p: 3 }}>
        <Typography color="textSecondary">
          Bulk upload interface will be implemented here...
        </Typography>
      </Paper>
    </Box>
  );
};

export default BulkUploadPage;
