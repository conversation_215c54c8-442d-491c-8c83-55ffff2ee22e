import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  useTheme,
} from '@mui/material';
import {
  TrendingUp,
  People,
  Quiz,
  Assessment,
  Today,
  Star,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

import { StatsCard } from '../../components/dashboard/StatsCard';
import { ChartCard } from '../../components/dashboard/ChartCard';
import { RecentActivity } from '../../components/dashboard/RecentActivity';
import { TopPerformers } from '../../components/dashboard/TopPerformers';
import { QuickActions } from '../../components/dashboard/QuickActions';

const DashboardPage: React.FC = () => {
  const theme = useTheme();

  // Mock data - replace with actual API calls
  const stats = [
    {
      title: 'Total Users',
      value: '1,234',
      change: '+12%',
      changeType: 'positive' as const,
      icon: People,
      color: theme.palette.primary.main,
    },
    {
      title: 'Total Questions',
      value: '5,678',
      change: '+8%',
      changeType: 'positive' as const,
      icon: Quiz,
      color: theme.palette.secondary.main,
    },
    {
      title: 'Quizzes Taken',
      value: '12,345',
      change: '+15%',
      changeType: 'positive' as const,
      icon: Assessment,
      color: theme.palette.success.main,
    },
    {
      title: 'Daily Active Users',
      value: '456',
      change: '-3%',
      changeType: 'negative' as const,
      icon: Today,
      color: theme.palette.warning.main,
    },
    {
      title: 'Average Score',
      value: '78.5%',
      change: '+2%',
      changeType: 'positive' as const,
      icon: Star,
      color: theme.palette.error.main,
    },
    {
      title: 'Completion Rate',
      value: '85.2%',
      change: '+5%',
      changeType: 'positive' as const,
      icon: TrendingUp,
      color: theme.palette.info.main,
    },
  ];

  const chartData = {
    userGrowth: [
      { month: 'Jan', users: 100 },
      { month: 'Feb', users: 150 },
      { month: 'Mar', users: 200 },
      { month: 'Apr', users: 280 },
      { month: 'May', users: 350 },
      { month: 'Jun', users: 420 },
    ],
    quizActivity: [
      { day: 'Mon', quizzes: 45 },
      { day: 'Tue', quizzes: 52 },
      { day: 'Wed', quizzes: 38 },
      { day: 'Thu', quizzes: 61 },
      { day: 'Fri', quizzes: 55 },
      { day: 'Sat', quizzes: 42 },
      { day: 'Sun', quizzes: 35 },
    ],
    categoryPerformance: [
      { category: 'General Knowledge', score: 85 },
      { category: 'PO Guide', score: 78 },
      { category: 'Volumes', score: 72 },
      { category: 'Previous Papers', score: 80 },
    ],
  };

  return (
    <>
      <Helmet>
        <title>Dashboard - MCQ Quiz Admin</title>
      </Helmet>
      
      <Box sx={{ flexGrow: 1, p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Welcome back! Here's what's happening with your quiz system.
          </Typography>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
              <StatsCard {...stat} />
            </Grid>
          ))}
        </Grid>

        {/* Charts and Analytics */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {/* User Growth Chart */}
          <Grid item xs={12} md={8}>
            <ChartCard
              title="User Growth"
              subtitle="Monthly user registration"
              data={chartData.userGrowth}
              type="line"
              dataKey="users"
              xAxisKey="month"
              color={theme.palette.primary.main}
            />
          </Grid>

          {/* Quick Actions */}
          <Grid item xs={12} md={4}>
            <QuickActions />
          </Grid>
        </Grid>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          {/* Quiz Activity Chart */}
          <Grid item xs={12} md={6}>
            <ChartCard
              title="Weekly Quiz Activity"
              subtitle="Quizzes taken this week"
              data={chartData.quizActivity}
              type="bar"
              dataKey="quizzes"
              xAxisKey="day"
              color={theme.palette.secondary.main}
            />
          </Grid>

          {/* Category Performance Chart */}
          <Grid item xs={12} md={6}>
            <ChartCard
              title="Category Performance"
              subtitle="Average scores by category"
              data={chartData.categoryPerformance}
              type="bar"
              dataKey="score"
              xAxisKey="category"
              color={theme.palette.success.main}
            />
          </Grid>
        </Grid>

        <Grid container spacing={3}>
          {/* Recent Activity */}
          <Grid item xs={12} md={8}>
            <RecentActivity />
          </Grid>

          {/* Top Performers */}
          <Grid item xs={12} md={4}>
            <TopPerformers />
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default DashboardPage;
