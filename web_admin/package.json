{"name": "mcq-quiz-admin", "version": "1.0.0", "description": "MCQ Quiz System - Web Admin Panel", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "firebase": "^10.7.1", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-charts": "^6.18.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "axios": "^1.6.2", "react-dropzone": "^14.2.3", "react-csv": "^2.2.2", "jspdf": "^2.5.1", "xlsx": "^0.18.5", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "react-helmet-async": "^1.3.0", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "dayjs": "^1.11.10"}, "devDependencies": {"@types/lodash": "^4.14.202", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,json,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5001"}