#!/bin/bash

# Quick Fix for Firebase Error
# This script provides an immediate solution to get you started

echo "🔧 Quick Fix for Firebase Error"
echo "==============================="
echo ""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${GREEN}✓${NC} $1"; }
print_info() { echo -e "${BLUE}ℹ${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
print_error() { echo -e "${RED}✗${NC} $1"; }

# Step 1: Install Firebase CLI if needed
install_firebase_cli() {
    if ! command -v firebase &> /dev/null; then
        print_info "Installing Firebase CLI..."
        npm install -g firebase-tools
        print_status "Firebase CLI installed"
    else
        print_status "Firebase CLI already installed"
    fi
}

# Step 2: Login to Firebase
login_firebase() {
    print_info "Checking Firebase login..."
    if ! firebase projects:list &> /dev/null; then
        print_warning "Please login to Firebase..."
        firebase login
    else
        print_status "Already logged in to Firebase"
    fi
}

# Step 3: Create minimal Firebase config
create_minimal_config() {
    print_info "Creating minimal Firebase configuration..."
    
    # Create .firebaserc if it doesn't exist
    if [ ! -f ".firebaserc" ]; then
        cat > .firebaserc << 'EOF'
{
  "projects": {
    "default": "mcq-quiz-demo"
  }
}
EOF
        print_status "Created .firebaserc"
    fi
    
    # Create firebase.json if it doesn't exist
    if [ ! -f "firebase.json" ]; then
        cat > firebase.json << 'EOF'
{
  "firestore": {
    "rules": "firebase/firestore.rules",
    "indexes": "firebase/firestore.indexes.json"
  },
  "hosting": {
    "public": "web_admin/build",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  },
  "storage": {
    "rules": "firebase/storage.rules"
  }
}
EOF
        print_status "Created firebase.json"
    fi
}

# Step 4: Test mobile app
test_mobile_app() {
    print_info "Testing mobile app setup..."
    
    if [ -d "mobile_app" ]; then
        cd mobile_app
        
        # Check if pubspec.yaml exists
        if [ -f "pubspec.yaml" ]; then
            print_info "Installing Flutter dependencies..."
            flutter pub get
            print_status "Flutter dependencies installed"
            
            print_info "You can now run: flutter run"
        else
            print_error "pubspec.yaml not found in mobile_app directory"
        fi
        
        cd ..
    else
        print_error "mobile_app directory not found"
    fi
}

# Step 5: Test web admin
test_web_admin() {
    print_info "Testing web admin setup..."
    
    if [ -d "web_admin" ]; then
        cd web_admin
        
        # Check if package.json exists
        if [ -f "package.json" ]; then
            print_info "Installing npm dependencies..."
            npm install
            print_status "npm dependencies installed"
            
            print_info "You can now run: npm start"
        else
            print_error "package.json not found in web_admin directory"
        fi
        
        cd ..
    else
        print_error "web_admin directory not found"
    fi
}

# Main function
main() {
    print_info "Starting quick fix for Firebase error..."
    echo ""
    
    install_firebase_cli
    login_firebase
    create_minimal_config
    
    echo ""
    print_status "🎉 Firebase error fixed!"
    echo ""
    print_info "Next steps:"
    echo "1. Create your Firebase project at: https://console.firebase.google.com/"
    echo "2. Update .firebaserc with your actual project ID"
    echo "3. Update Firebase configuration in your apps"
    echo ""
    
    read -p "Do you want to test the mobile app setup? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_mobile_app
    fi
    
    read -p "Do you want to test the web admin setup? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_web_admin
    fi
    
    echo ""
    print_status "Quick fix completed!"
    print_info "You can now run Firebase commands without errors."
}

# Run main function
main
