#!/bin/bash

# Firebase Initialization Script
# This script initializes Firebase in your project directory

echo "🔥 Firebase Initialization for MCQ Quiz System"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Check if Firebase CLI is installed
check_firebase_cli() {
    if ! command -v firebase &> /dev/null; then
        print_error "Firebase CLI is not installed"
        print_info "Installing Firebase CLI..."
        npm install -g firebase-tools
        print_status "Firebase CLI installed"
    else
        print_status "Firebase CLI is already installed"
    fi
}

# Check if user is logged in
check_firebase_login() {
    print_info "Checking Firebase login status..."
    if ! firebase projects:list &> /dev/null; then
        print_warning "You are not logged in to Firebase"
        print_info "Please login to Firebase..."
        firebase login
        
        # Check again after login
        if ! firebase projects:list &> /dev/null; then
            print_error "Firebase login failed"
            exit 1
        fi
    fi
    print_status "Firebase login verified"
}

# Initialize Firebase in the project
init_firebase_project() {
    print_info "Initializing Firebase in your project..."
    
    # Check if already initialized
    if [ -f ".firebaserc" ]; then
        print_warning "Firebase is already initialized in this project"
        read -p "Do you want to reinitialize? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Skipping Firebase initialization"
            return 0
        fi
    fi
    
    # Run Firebase init
    print_info "Running Firebase initialization..."
    print_warning "When prompted, select these services:"
    echo "  ✓ Firestore: Configure security rules and indexes files"
    echo "  ✓ Functions: Configure a Cloud Functions directory and files"
    echo "  ✓ Hosting: Configure files for Firebase Hosting"
    echo "  ✓ Storage: Configure a security rules file for Cloud Storage"
    echo ""
    print_info "Press Enter to continue..."
    read
    
    firebase init
    
    if [ $? -eq 0 ]; then
        print_status "Firebase initialized successfully"
    else
        print_error "Firebase initialization failed"
        exit 1
    fi
}

# Create or update Firebase configuration
setup_firebase_config() {
    print_info "Setting up Firebase configuration..."
    
    # Move to firebase directory if it exists
    if [ -d "firebase" ]; then
        cd firebase
    fi
    
    # Copy our pre-configured files if they don't exist
    if [ ! -f "firestore.rules" ] && [ -f "../firebase/firestore.rules" ]; then
        cp ../firebase/firestore.rules ./
        print_status "Firestore rules copied"
    fi
    
    if [ ! -f "firestore.indexes.json" ] && [ -f "../firebase/firestore.indexes.json" ]; then
        cp ../firebase/firestore.indexes.json ./
        print_status "Firestore indexes copied"
    fi
    
    if [ ! -f "storage.rules" ] && [ -f "../firebase/storage.rules" ]; then
        cp ../firebase/storage.rules ./
        print_status "Storage rules copied"
    fi
    
    # Go back to root if we changed directory
    if [ -d "../mobile_app" ]; then
        cd ..
    fi
}

# Test Firebase connection
test_firebase_connection() {
    print_info "Testing Firebase connection..."
    
    # Try to list projects
    if firebase projects:list > /dev/null 2>&1; then
        print_status "Firebase connection successful"
        
        # Show current project
        CURRENT_PROJECT=$(firebase use 2>/dev/null || echo "No project selected")
        print_info "Current project: $CURRENT_PROJECT"
        
        if [ "$CURRENT_PROJECT" = "No project selected" ]; then
            print_warning "No Firebase project selected"
            print_info "Available projects:"
            firebase projects:list
            echo ""
            read -p "Enter the project ID you want to use: " PROJECT_ID
            firebase use "$PROJECT_ID"
            
            if [ $? -eq 0 ]; then
                print_status "Project selected: $PROJECT_ID"
            else
                print_error "Failed to select project"
                exit 1
            fi
        fi
    else
        print_error "Firebase connection failed"
        exit 1
    fi
}

# Main function
main() {
    echo "Starting Firebase initialization..."
    echo ""
    
    check_firebase_cli
    check_firebase_login
    init_firebase_project
    setup_firebase_config
    test_firebase_connection
    
    echo ""
    print_status "🎉 Firebase initialization completed!"
    echo ""
    print_info "Next steps:"
    echo "1. Configure your Firebase project services in the console"
    echo "2. Update your app configuration files"
    echo "3. Run the mobile app setup: cd mobile_app && flutter pub get"
    echo "4. Run the web admin setup: cd web_admin && npm install"
    echo ""
    print_info "Firebase Console: https://console.firebase.google.com/"
    echo ""
}

# Run main function
main
