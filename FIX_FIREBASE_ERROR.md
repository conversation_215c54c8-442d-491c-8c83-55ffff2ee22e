# 🔧 Fix Firebase Error - Quick Solution

You're getting the error: `firebase use must be run from a Firebase project directory`

This happens because Firebase CLI needs to be initialized in your project first. Here's how to fix it:

## 🚀 Quick Fix (2 minutes)

### Step 1: Initialize Firebase
```bash
# Make the script executable
chmod +x firebase-init.sh

# Run Firebase initialization
./firebase-init.sh
```

### Step 2: Manual Firebase Setup (Alternative)
If the script doesn't work, do this manually:

```bash
# 1. Install Firebase CLI (if not installed)
npm install -g firebase-tools

# 2. Login to Firebase
firebase login

# 3. Initialize Firebase in your project
firebase init
```

When prompted during `firebase init`, select:
- ✅ Firestore: Configure security rules and indexes
- ✅ Functions: Configure Cloud Functions
- ✅ Hosting: Configure Firebase Hosting  
- ✅ Storage: Configure Cloud Storage

### Step 3: Select Your Firebase Project
```bash
# List available projects
firebase projects:list

# Use your project (replace with your project ID)
firebase use your-project-id
```

## 🔥 Create Firebase Project (If You Don't Have One)

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name (e.g., "mcq-quiz-system")
4. Enable Google Analytics (optional)
5. Create project

## ⚙️ Enable Required Services

In Firebase Console, enable these services:

### 1. Authentication
- Go to Authentication → Sign-in method
- Enable "Phone" provider

### 2. Firestore Database
- Go to Firestore Database
- Create database in production mode
- Choose your region

### 3. Storage
- Go to Storage
- Get started with default rules

### 4. Hosting
- Go to Hosting
- Get started

## 📱 Update Mobile App Configuration

After Firebase is set up, update your mobile app:

### 1. Download Configuration Files

**For Android:**
- Go to Project Settings → Your apps
- Download `google-services.json`
- Place in `mobile_app/android/app/`

**For iOS:**
- Download `GoogleService-Info.plist`
- Add to `mobile_app/ios/Runner/` in Xcode

### 2. Update Firebase Options
Update `mobile_app/lib/core/config/firebase_options.dart` with your actual Firebase config:

```dart
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'your-actual-api-key',
  appId: 'your-actual-app-id',
  messagingSenderId: 'your-sender-id',
  projectId: 'your-project-id',
  storageBucket: 'your-project-id.appspot.com',
);
```

## 🌐 Update Web Admin Configuration

Create `web_admin/.env.local`:

```env
REACT_APP_FIREBASE_API_KEY=your-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
REACT_APP_FIREBASE_APP_ID=your-app-id
```

## 🧪 Test Your Setup

### Test Mobile App
```bash
cd mobile_app
flutter pub get
flutter run
```

### Test Web Admin
```bash
cd web_admin
npm install
npm start
```

## 🆘 Still Having Issues?

### Common Solutions:

1. **Node.js Deprecation Warning**: This is just a warning, you can ignore it
2. **Firebase CLI Issues**: 
   ```bash
   npm uninstall -g firebase-tools
   npm install -g firebase-tools@latest
   ```
3. **Permission Issues**: Make sure you're the owner of the Firebase project
4. **Network Issues**: Check your internet connection

### Check Your Setup:
```bash
# Verify Firebase CLI
firebase --version

# Check login status
firebase projects:list

# Check current project
firebase use
```

## 📞 Need Help?

If you're still stuck:
1. Check the error message carefully
2. Make sure you're in the correct directory
3. Verify your Firebase project exists
4. Check your internet connection

## ✅ Success Indicators

You'll know it's working when:
- `firebase use` shows your project ID
- `firebase projects:list` shows your projects
- No errors when running the setup scripts

---

**Quick Start:** Run `./firebase-init.sh` and follow the prompts!
