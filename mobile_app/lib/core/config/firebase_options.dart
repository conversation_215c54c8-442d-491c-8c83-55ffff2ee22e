// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: "AIzaSyDIdFTL8Xl-E02bYB_HnuymfGBRRL6xBqk",
    authDomain: "mcq-quiz-system.firebaseapp.com",
    projectId: "mcq-quiz-system",
    storageBucket: "mcq-quiz-system.firebasestorage.app",
    messagingSenderId: "109048215498",
    appId: "1:109048215498:web:398b38704a2b075fb08133",
    measurementId: "G-F5Z833J800",
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDIdFTL8Xl-E02bYB_HnuymfGBRRL6xBqk',
    appId: '1:109048215498:android:c0ac280012cca252b08133',
    messagingSenderId: '109048215498',
    projectId: 'mcq-quiz-system',
    storageBucket: 'mcq-quiz-system.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDIdFTL8Xl-E02bYB_HnuymfGBRRL6xBqk',
    appId: '1:109048215498:ios:YOUR_IOS_APP_ID',
    messagingSenderId: '109048215498',
    projectId: 'mcq-quiz-system',
    storageBucket: 'mcq-quiz-system.firebasestorage.app',
    iosBundleId: 'com.mcqquiz.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDIdFTL8Xl-E02bYB_HnuymfGBRRL6xBqk',
    appId: '1:109048215498:ios:YOUR_IOS_APP_ID',
    messagingSenderId: '109048215498',
    projectId: 'mcq-quiz-system',
    storageBucket: 'mcq-quiz-system.firebasestorage.app',
    iosBundleId: 'com.mcqquiz.app',
  );
}
