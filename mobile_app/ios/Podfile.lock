PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Auth (10.12.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.12.0)
  - Firebase/CoreOnly (10.12.0):
    - FirebaseCore (= 10.12.0)
  - firebase_auth (4.7.3):
    - Firebase/Auth (= 10.12.0)
    - firebase_core
    - Flutter
  - firebase_core (2.15.1):
    - Firebase/CoreOnly (= 10.12.0)
    - Flutter
  - FirebaseAppCheckInterop (10.29.0)
  - FirebaseAuth (10.12.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseCore (10.12.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - screen_protector (1.2.1):
    - Flutter
    - ScreenProtectorKit (~> 1.3.1)
  - ScreenProtectorKit (1.3.1)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - smart_auth (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - screen_protector (from `.symlinks/plugins/screen_protector/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - smart_auth (from `.symlinks/plugins/smart_auth/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - GoogleUtilities
    - GTMSessionFetcher
    - PromisesObjC
    - ReachabilitySwift
    - ScreenProtectorKit

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  screen_protector:
    :path: ".symlinks/plugins/screen_protector/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  smart_auth:
    :path: ".symlinks/plugins/smart_auth/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"

SPEC CHECKSUMS:
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  Firebase: 07150e75d142fb9399f6777fa56a187b17f833a0
  firebase_auth: 95f4a6acafa6a7a0d902aa467dd4d7ca336ee4be
  firebase_core: 4a3246a02f828a01c74a2c26427037786d90f17f
  FirebaseAppCheckInterop: 6a1757cfd4067d8e00fccd14fcc1b8fd78cfac07
  FirebaseAuth: a66c1e14ec58f41d154a4b41ce1a23ea00ad4805
  FirebaseCore: f86a1394906b97ac445ae49c92552a9425831bed
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  local_auth_darwin: 66e40372f1c29f383a314c738c7446e2f7fdadc3
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  screen_protector: 6f92086bd2f2f4b54f54913289b9d1310610140b
  ScreenProtectorKit: 83a6281b02c7a5902ee6eac4f5045f674e902ae4
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  smart_auth: 4bedbc118723912d0e45a07e8ab34039c19e04f2
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d

PODFILE CHECKSUM: 23e815079e1cb6e79806f77b2ec93f9e262ac10f

COCOAPODS: 1.16.2
