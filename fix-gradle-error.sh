#!/bin/bash

# Fix Flutter Gradle Plugin Error
echo "🔧 Fixing Flutter Gradle Plugin Error"
echo "====================================="
echo ""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${GREEN}✓${NC} $1"; }
print_info() { echo -e "${BLUE}ℹ${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
print_error() { echo -e "${RED}✗${NC} $1"; }

# Clean Flutter project
clean_flutter() {
    print_info "Cleaning Flutter project..."
    cd mobile_app
    
    flutter clean
    print_status "Flutter clean completed"
    
    # Remove build directories
    rm -rf build/
    rm -rf android/.gradle/
    rm -rf android/app/build/
    print_status "Build directories cleaned"
    
    cd ..
}

# Get Flutter packages
get_packages() {
    print_info "Getting Flutter packages..."
    cd mobile_app
    
    flutter pub get
    print_status "Flutter packages updated"
    
    cd ..
}

# Test the build
test_build() {
    print_info "Testing Android build..."
    cd mobile_app
    
    print_warning "This may take a few minutes..."
    flutter build apk --debug
    
    if [ $? -eq 0 ]; then
        print_status "Build successful! 🎉"
    else
        print_error "Build failed. Check the error messages above."
        return 1
    fi
    
    cd ..
}

# Run the app
run_app() {
    print_info "Running the app..."
    cd mobile_app
    
    print_info "Available devices:"
    flutter devices
    
    echo ""
    read -p "Do you want to run the app now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        flutter run
    else
        print_info "You can run the app later using: cd mobile_app && flutter run"
    fi
    
    cd ..
}

# Main function
main() {
    print_info "This script will fix the Flutter Gradle plugin error"
    echo ""
    
    print_warning "Changes made:"
    echo "• Updated build.gradle to use declarative plugins syntax"
    echo "• Updated Android Gradle Plugin to 8.1.0"
    echo "• Updated Kotlin version to 1.9.10"
    echo "• Updated Google Services plugin to 4.4.0"
    echo ""
    
    clean_flutter
    get_packages
    
    echo ""
    read -p "Do you want to test the build now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        test_build
        
        if [ $? -eq 0 ]; then
            run_app
        fi
    else
        print_info "You can test the build later using: cd mobile_app && flutter build apk --debug"
    fi
    
    echo ""
    print_status "🎉 Gradle error fix completed!"
    echo ""
    print_info "If you still get errors:"
    echo "1. Make sure you have the latest Flutter version: flutter upgrade"
    echo "2. Check Android Studio is updated"
    echo "3. Try: cd mobile_app && flutter doctor"
    echo ""
}

# Run main function
main
