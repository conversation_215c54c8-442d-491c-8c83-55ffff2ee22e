#!/bin/bash

# Extract Firebase Configuration Values
echo "🔥 Firebase Configuration Extractor"
echo "==================================="
echo ""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${GREEN}✓${NC} $1"; }
print_info() { echo -e "${BLUE}ℹ${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
print_error() { echo -e "${RED}✗${NC} $1"; }

# Extract from google-services.json
extract_android_config() {
    GOOGLE_SERVICES_FILE="mobile_app/android/app/google-services.json"
    
    if [ -f "$GOOGLE_SERVICES_FILE" ]; then
        print_status "Found google-services.json"
        echo ""
        
        # Extract values using jq if available, otherwise use grep
        if command -v jq &> /dev/null; then
            print_info "📱 Android Configuration:"
            echo "========================"
            
            API_KEY=$(jq -r '.client[0].api_key[0].current_key' "$GOOGLE_SERVICES_FILE")
            APP_ID=$(jq -r '.client[0].client_info.mobilesdk_app_id' "$GOOGLE_SERVICES_FILE")
            PROJECT_ID=$(jq -r '.project_info.project_id' "$GOOGLE_SERVICES_FILE")
            SENDER_ID=$(jq -r '.project_info.project_number' "$GOOGLE_SERVICES_FILE")
            STORAGE_BUCKET=$(jq -r '.project_info.storage_bucket' "$GOOGLE_SERVICES_FILE")
            
            echo "apiKey: '$API_KEY'"
            echo "appId: '$APP_ID'"
            echo "messagingSenderId: '$SENDER_ID'"
            echo "projectId: '$PROJECT_ID'"
            echo "storageBucket: '$STORAGE_BUCKET'"
            
        else
            print_info "📱 Raw google-services.json content:"
            echo "===================================="
            cat "$GOOGLE_SERVICES_FILE"
        fi
        
        echo ""
        
    else
        print_warning "google-services.json not found"
        print_info "Please download it from Firebase Console:"
        echo "1. Go to Firebase Console → Project Settings"
        echo "2. Select your Android app"
        echo "3. Download google-services.json"
        echo "4. Place it in mobile_app/android/app/"
        echo ""
    fi
}

# Show web config template
show_web_config_template() {
    print_info "🌐 Web Configuration Template:"
    echo "============================="
    echo ""
    echo "Create web_admin/.env.local with:"
    echo ""
    cat << 'EOF'
REACT_APP_FIREBASE_API_KEY=your-api-key-here
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
REACT_APP_FIREBASE_APP_ID=your-web-app-id
EOF
    echo ""
}

# Generate Flutter config template
generate_flutter_config() {
    print_info "📱 Flutter Configuration Template:"
    echo "=================================="
    echo ""
    echo "Update mobile_app/lib/core/config/firebase_options.dart:"
    echo ""
    
    cat << 'EOF'
static const FirebaseOptions android = FirebaseOptions(
  apiKey: 'your-android-api-key',
  appId: 'your-android-app-id',
  messagingSenderId: 'your-sender-id',
  projectId: 'your-project-id',
  storageBucket: 'your-project-id.appspot.com',
);

static const FirebaseOptions ios = FirebaseOptions(
  apiKey: 'your-ios-api-key',
  appId: 'your-ios-app-id',
  messagingSenderId: 'your-sender-id',
  projectId: 'your-project-id',
  storageBucket: 'your-project-id.appspot.com',
  iosBundleId: 'com.mcqquiz.app',
);

static const FirebaseOptions web = FirebaseOptions(
  apiKey: 'your-web-api-key',
  appId: 'your-web-app-id',
  messagingSenderId: 'your-sender-id',
  projectId: 'your-project-id',
  authDomain: 'your-project-id.firebaseapp.com',
  storageBucket: 'your-project-id.appspot.com',
  measurementId: 'your-measurement-id',
);
EOF
    echo ""
}

# Main function
main() {
    print_info "This script helps you extract Firebase configuration values"
    echo ""
    
    extract_android_config
    show_web_config_template
    generate_flutter_config
    
    print_status "🎯 How to Get These Values:"
    echo "=========================="
    echo "1. Go to https://console.firebase.google.com/"
    echo "2. Select your project"
    echo "3. Click Settings ⚙️ → Project settings"
    echo "4. Scroll to 'Your apps' section"
    echo "5. Add apps for Android, iOS, and Web"
    echo "6. Copy the configuration values"
    echo ""
    
    print_warning "📋 Checklist:"
    echo "□ Create Firebase project"
    echo "□ Add Android app (package: com.mcqquiz.app)"
    echo "□ Add Web app"
    echo "□ Download google-services.json"
    echo "□ Copy web config values"
    echo "□ Update firebase_options.dart"
    echo "□ Create .env.local for web admin"
    echo ""
}

# Run main function
main
