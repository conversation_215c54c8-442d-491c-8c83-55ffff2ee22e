# MCQ Quiz System - Git Ignore File

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Firebase
.firebase/
.firebaserc
firebase-debug.log
firebase-debug.*.log

# Flutter/Dart
mobile_app/.dart_tool/
mobile_app/.flutter-plugins
mobile_app/.flutter-plugins-dependencies
mobile_app/.packages
mobile_app/.pub-cache/
mobile_app/.pub/
mobile_app/build/
mobile_app/ios/Flutter/flutter_export_environment.sh
mobile_app/ios/Flutter/Generated.xcconfig
mobile_app/ios/Flutter/flutter_export_environment.sh

# Android
mobile_app/android/app/debug
mobile_app/android/app/profile
mobile_app/android/app/release
mobile_app/android/.gradle
mobile_app/android/captures/
mobile_app/android/gradlew
mobile_app/android/gradlew.bat
mobile_app/android/local.properties
mobile_app/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
mobile_app/android/key.properties

# iOS
mobile_app/ios/Flutter/App.framework
mobile_app/ios/Flutter/Flutter.framework
mobile_app/ios/Flutter/Flutter.podspec
mobile_app/ios/Flutter/Generated.xcconfig
mobile_app/ios/Flutter/app.flx
mobile_app/ios/Flutter/app.zip
mobile_app/ios/Flutter/flutter_assets/
mobile_app/ios/Flutter/flutter_export_environment.sh
mobile_app/ios/ServiceDefinitions.json
mobile_app/ios/Runner/GeneratedPluginRegistrant.*
mobile_app/ios/Pods/
mobile_app/ios/.symlinks/
mobile_app/ios/Flutter/flutter_export_environment.sh

# Web Admin Panel
web_admin/build/
web_admin/dist/
web_admin/.env.local
web_admin/.env.development.local
web_admin/.env.test.local
web_admin/.env.production.local

# Firebase Functions
firebase/functions/lib/
firebase/functions/.env
firebase/functions/.env.local

# Testing
coverage/
.nyc_output/
junit.xml

# Temporary files
*.tmp
*.temp
.cache/

# Documentation build
docs/_build/
docs/.doctrees/

# Backup files
*.bak
*.backup
*~

# Security and sensitive files
*.pem
*.key
*.p12
*.mobileprovision
google-services.json
GoogleService-Info.plist
firebase-adminsdk-*.json

# Build artifacts
*.apk
*.aab
*.ipa
*.app

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Package files
*.tar.gz
*.zip
*.rar

# Database
*.db
*.sqlite
*.sqlite3

# Certificates
*.crt
*.cer
*.p7b
*.p7c
*.pfx

# Local configuration
config.local.json
settings.local.json

# Temporary directories
tmp/
temp/
.tmp/

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# MacOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Project specific
shared/secrets/
docs/private/
scripts/deploy-keys/

# Analytics and tracking
analytics.json
tracking.json

# Performance monitoring
performance.json
metrics.json

# User uploads (if stored locally)
uploads/
user-content/

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
