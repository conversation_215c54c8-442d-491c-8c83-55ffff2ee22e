# MCQ Quiz System for Post Office Departmental Exam

A comprehensive quiz system designed for post office departmental exam preparation, featuring a Flutter mobile app for students and a React web admin panel for teachers/admins.

## 🎯 Project Overview

This system consists of:
- **Mobile App (Flutter)**: Student-facing quiz application
- **Web Admin Panel (React + Firebase)**: Teacher/admin interface for managing questions and analytics
- **Backend (Firebase)**: Real-time database and authentication

## 📱 Mobile App Features

### Core Features
- Clean, modern UI with onboarding screens
- Login/Signup with OTP verification
- Dashboard with quiz categories and banners
- Interactive quiz interface with timer
- Result analysis with correct/wrong answers highlighted
- User profile and quiz history
- Leaderboard functionality

### Advanced Features
- Timer logs and screen lock detection (Android)
- Screenshot detection and prevention
- Exam mode with navigation restrictions
- Dark mode support
- Push notifications
- Bookmark difficult questions
- Adaptive difficulty system

### Quiz Categories
- Volumes
- PO Guide
- General Knowledge
- Previous year question papers
- Exam pattern specific (MTS, POSTMAN, Postal Assistant, IPO, Group B)

## 🖥️ Web Admin Panel Features

### Dashboard & Analytics
- Total questions, active users, quizzes played
- Quiz participation trends
- Top-performing questions analysis
- Drop-off rate tracking
- Export results as PDF/Excel

### Question Management
- Create/Edit/Delete questions
- Category and sub-category management
- Bulk upload via Excel/CSV
- Difficulty level tagging
- Question pooling for randomized quizzes

### User Management
- View user performance and history
- Role-based access (admin, master admin, user)
- Performance tracking and analytics

## 🗃️ Backend Architecture

### Firebase Services
- **Firestore**: Real-time database for questions, users, and results
- **Authentication**: User management with OTP verification
- **Cloud Functions**: Server-side logic for complex operations
- **Storage**: File uploads and media management

### Data Structure
- Users collection with roles and performance data
- Questions collection with categories and metadata
- Quiz sessions with real-time tracking
- Analytics collection for reporting

## 🎨 Design System

### Color Scheme
- Primary: Soft purple (#6366F1)
- Secondary: White (#FFFFFF)
- Accent: Light purple (#E0E7FF)
- Text: Dark gray (#374151)

### UI Components
- Rounded cards with clean spacing
- Bottom navigation with icons
- Progress indicators and timers
- Onboarding illustrations

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (latest stable)
- Node.js and npm
- Firebase CLI
- Android Studio / Xcode for mobile development

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd MCQ
```

2. Set up Firebase project
```bash
firebase init
```

3. Install mobile app dependencies
```bash
cd mobile_app
flutter pub get
```

4. Install web admin dependencies
```bash
cd ../web_admin
npm install
```

### Running the Applications

#### Mobile App
```bash
cd mobile_app
flutter run
```

#### Web Admin Panel
```bash
cd web_admin
npm start
```

## 📁 Project Structure

```
MCQ/
├── mobile_app/          # Flutter mobile application
├── web_admin/           # React web admin panel
├── firebase/            # Firebase configuration and functions
├── docs/               # Documentation and design assets
├── shared/             # Shared utilities and constants
└── README.md           # This file
```

## 🔧 Configuration

### Environment Variables
Create `.env` files in respective directories with:
- Firebase configuration
- API keys
- Environment-specific settings

### Firebase Rules
Security rules are configured for:
- User authentication
- Role-based access control
- Data validation

## 📊 Performance Features

### Tracking Metrics
- Accuracy percentage
- Time spent per question
- Strengths and weaknesses by topic
- Progress over time

### Engagement Features
- Daily challenges
- Streak badges and rewards
- Adaptive quiz difficulty
- Review incorrect answers

## 🛡️ Security Features

### Mobile App Security
- Screen recording prevention
- Screenshot detection
- App backgrounding detection
- Exam mode restrictions

### Data Security
- Firebase security rules
- Role-based access control
- Data encryption
- Secure authentication

## 📈 Analytics & Reporting

### Admin Analytics
- User engagement metrics
- Question performance analysis
- Category-wise statistics
- Export capabilities

### Student Analytics
- Personal performance tracking
- Progress visualization
- Weakness identification
- Improvement suggestions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.
