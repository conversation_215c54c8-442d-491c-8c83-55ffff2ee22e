#!/bin/bash

# Get SHA-1 Certificate for Firebase Android Setup
echo "🔐 Getting SHA-1 Certificate for Firebase"
echo "========================================"
echo ""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() { echo -e "${GREEN}✓${NC} $1"; }
print_info() { echo -e "${BLUE}ℹ${NC} $1"; }
print_warning() { echo -e "${YELLOW}⚠${NC} $1"; }
print_error() { echo -e "${RED}✗${NC} $1"; }

# Check if keytool is available
check_keytool() {
    if ! command -v keytool &> /dev/null; then
        print_error "keytool not found. Please install Java JDK"
        exit 1
    fi
    print_status "keytool found"
}

# Get debug SHA-1
get_debug_sha1() {
    print_info "Getting debug SHA-1 certificate..."
    echo ""
    
    # Try different locations for debug keystore
    DEBUG_KEYSTORE_LOCATIONS=(
        "$HOME/.android/debug.keystore"
        "$ANDROID_HOME/debug.keystore"
        "~/.android/debug.keystore"
    )
    
    DEBUG_KEYSTORE=""
    for location in "${DEBUG_KEYSTORE_LOCATIONS[@]}"; do
        if [ -f "$location" ]; then
            DEBUG_KEYSTORE="$location"
            break
        fi
    done
    
    if [ -z "$DEBUG_KEYSTORE" ]; then
        print_warning "Debug keystore not found. Creating one..."
        mkdir -p "$HOME/.android"
        keytool -genkey -v -keystore "$HOME/.android/debug.keystore" -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000 -dname "CN=Android Debug,O=Android,C=US"
        DEBUG_KEYSTORE="$HOME/.android/debug.keystore"
    fi
    
    print_status "Debug keystore found: $DEBUG_KEYSTORE"
    echo ""
    
    print_info "Debug SHA-1 Certificate:"
    echo "========================"
    keytool -list -v -keystore "$DEBUG_KEYSTORE" -alias androiddebugkey -storepass android -keypass android | grep SHA1 | head -1
    echo ""
}

# Get release SHA-1 (if exists)
get_release_sha1() {
    print_info "Checking for release keystore..."
    
    RELEASE_KEYSTORE="mobile_app/android/app/upload-keystore.jks"
    
    if [ -f "$RELEASE_KEYSTORE" ]; then
        print_status "Release keystore found"
        echo ""
        read -p "Enter release keystore password: " -s STORE_PASS
        echo ""
        read -p "Enter key alias: " KEY_ALIAS
        read -p "Enter key password: " -s KEY_PASS
        echo ""
        
        print_info "Release SHA-1 Certificate:"
        echo "========================="
        keytool -list -v -keystore "$RELEASE_KEYSTORE" -alias "$KEY_ALIAS" -storepass "$STORE_PASS" -keypass "$KEY_PASS" | grep SHA1 | head -1
        echo ""
    else
        print_warning "No release keystore found"
        print_info "To create a release keystore, run:"
        echo "keytool -genkey -v -keystore mobile_app/android/app/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload"
        echo ""
    fi
}

# Generate Flutter SHA-1
get_flutter_sha1() {
    print_info "Getting SHA-1 using Flutter command..."
    echo ""
    
    if command -v flutter &> /dev/null; then
        cd mobile_app
        print_info "Flutter SHA-1 Certificates:"
        echo "=========================="
        flutter run --debug --verbose 2>&1 | grep -i sha1 || {
            print_warning "No SHA-1 found in Flutter output"
            print_info "Try running: cd mobile_app && flutter run --debug"
        }
        cd ..
    else
        print_warning "Flutter not found"
    fi
}

# Main function
main() {
    echo "This script will help you get the SHA-1 certificates needed for Firebase Android setup"
    echo ""
    
    print_info "📱 Android Package Name: com.mcqquiz.app"
    echo ""
    
    check_keytool
    get_debug_sha1
    get_release_sha1
    
    echo ""
    print_status "🎉 SHA-1 Certificate Information Retrieved!"
    echo ""
    print_info "Next steps:"
    echo "1. Copy the SHA-1 certificate(s) above"
    echo "2. Go to Firebase Console → Project Settings → Your Android App"
    echo "3. Add the SHA-1 certificate(s) to your app"
    echo "4. Download the updated google-services.json"
    echo "5. Place it in mobile_app/android/app/"
    echo ""
    print_warning "Important:"
    echo "• Use the DEBUG SHA-1 for development"
    echo "• Use the RELEASE SHA-1 for production builds"
    echo "• You can add multiple SHA-1 certificates to the same app"
    echo ""
}

# Run main function
main
