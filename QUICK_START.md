# 🚀 MCQ Quiz System - Quick Start Guide

Welcome to the MCQ Quiz System! This guide will get you up and running in minutes.

## 📋 Prerequisites

Before you begin, make sure you have:

- [ ] **Firebase Account** - [Create one here](https://firebase.google.com/)
- [ ] **Node.js 16+** - [Download here](https://nodejs.org/)
- [ ] **Flutter SDK** - [Install guide](https://flutter.dev/docs/get-started/install)
- [ ] **Git** - [Download here](https://git-scm.com/)
- [ ] **Code Editor** - VS Code recommended

## ⚡ Quick Setup (5 Minutes)

### Step 1: Run the Setup Script
```bash
# Make the setup script executable
chmod +x setup.sh

# Run the interactive setup
./setup.sh
```

### Step 2: Follow the Setup Wizard
The setup script will guide you through:
1. 🔥 Firebase project creation and configuration
2. 📱 Mobile app setup with Flutter
3. 🌐 Web admin panel configuration
4. 🗄️ Database initialization with sample data
5. 👤 Admin user creation

### Step 3: Test Your Installation
```bash
# Test the mobile app
cd mobile_app
flutter run

# Test the web admin panel
cd ../web_admin
npm start
```

## 🎯 Manual Setup (If You Prefer)

### 1. Firebase Setup
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Run Firebase setup
bash scripts/setup-firebase.sh
```

### 2. Mobile App Setup
```bash
# Setup Flutter app
bash scripts/setup-mobile.sh

# Run the app
cd mobile_app
flutter run
```

### 3. Web Admin Setup
```bash
# Setup React admin panel
bash scripts/setup-web.sh

# Start development server
cd web_admin
npm start
```

### 4. Initialize Database
```bash
# Add sample data
node scripts/init-database.js

# Create admin user
node scripts/create-admin-user.js
```

## 🔧 Configuration

### Firebase Configuration
1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable these services:
   - Authentication (Phone Number)
   - Firestore Database
   - Storage
   - Hosting
   - Cloud Functions

### Environment Variables
Create these files with your Firebase config:

**Mobile App:** `mobile_app/lib/core/config/firebase_options.dart`
**Web Admin:** `web_admin/.env.local`

## 📱 Mobile App Features

- ✅ Onboarding screens
- ✅ Phone number authentication
- ✅ Quiz categories and dashboard
- ✅ Interactive quiz interface
- ✅ Results and analytics
- ✅ User profile and history
- ✅ Dark mode support
- ✅ Security features (screenshot detection)

## 🖥️ Web Admin Features

- ✅ Admin dashboard with analytics
- ✅ Question management (CRUD)
- ✅ Category management
- ✅ User management and analytics
- ✅ Bulk question upload (Excel/CSV)
- ✅ Export functionality
- ✅ Real-time monitoring

## 🗄️ Database Structure

The system uses Firebase Firestore with these main collections:
- `users` - User profiles and stats
- `questions` - Quiz questions and metadata
- `categories` - Question categories
- `quiz_sessions` - Active quiz sessions
- `quiz_results` - Completed quiz results
- `leaderboard` - User rankings
- `daily_challenges` - Daily quiz challenges

## 🚀 Deployment

### Development
```bash
# Start mobile app
cd mobile_app && flutter run

# Start web admin
cd web_admin && npm start

# Start Firebase emulators
cd firebase && firebase emulators:start
```

### Production
```bash
# Run production deployment
bash scripts/deploy-production.sh
```

## 🧪 Testing

```bash
# Test everything
bash scripts/test-system.sh

# Test mobile app only
cd mobile_app && flutter test

# Test web admin only
cd web_admin && npm test
```

## 📚 Documentation

- 📖 [Complete Setup Guide](docs/setup-guide.md)
- 🔧 [API Documentation](docs/api-documentation.md)
- 🗄️ [Database Structure](docs/firebase-data-structure.md)
- 🎨 [UI Design Guide](docs/ui-design-guide.md)
- 🚀 [Deployment Guide](docs/deployment-guide.md)

## 🆘 Troubleshooting

### Common Issues

**Firebase Login Issues:**
```bash
firebase logout
firebase login
```

**Flutter Issues:**
```bash
flutter doctor
flutter clean
flutter pub get
```

**Node.js Issues:**
```bash
rm -rf node_modules
npm install
```

**Build Issues:**
- Check all environment variables are set
- Verify Firebase project configuration
- Ensure all dependencies are installed

### Getting Help

1. Check the error messages carefully
2. Review the documentation in `docs/` folder
3. Run the test script: `bash scripts/test-system.sh`
4. Check Firebase Console for configuration issues

## 🎉 What's Next?

After setup is complete:

1. **Add Content**: Use the web admin panel to add quiz questions
2. **Customize**: Update colors, logos, and branding
3. **Test**: Thoroughly test all features
4. **Deploy**: Deploy to production when ready
5. **Monitor**: Set up monitoring and analytics

## 📞 Support

- 📧 Email: <EMAIL>
- 📖 Documentation: Check the `docs/` directory
- 🐛 Issues: Create an issue in the repository

## 🏆 Features Overview

### For Students (Mobile App)
- Take quizzes across multiple categories
- Track performance and progress
- Compete on leaderboards
- Daily challenges
- Bookmark difficult questions
- Detailed result analysis

### For Admins (Web Panel)
- Manage questions and categories
- View user analytics
- Bulk upload questions
- Export reports
- Monitor system health
- Manage user roles

### For Developers
- Modern tech stack (Flutter + React + Firebase)
- Comprehensive documentation
- Automated testing
- CI/CD ready
- Security best practices
- Scalable architecture

---

**Ready to get started?** Run `./setup.sh` and follow the interactive setup wizard!

🎯 **Goal**: Get your MCQ Quiz System running in under 10 minutes!
